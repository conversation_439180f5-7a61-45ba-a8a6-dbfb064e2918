<template>
  <div class="login">
    <div class="title-logo">
      <img width="100%" :src="titleLogo" />
    </div>
    <div class="slogon">
      <img width="100%" :src="slogon" />
    </div>
    <div class="login-form">
      <h3 class="title">厦门分行新一代综合管理平台</h3>

      <!-- 只能邮连登录为“是” -->
      <div v-if="uniloginFlag=='Y'" style="height: 150px; text-align: center; line-height: 120px;">
        <span style="color:red;font-size: 20px;">请前往邮连登录！</span>
      </div>

      <!-- 只能邮连登录为“否” -->
      <div v-if="uniloginFlag!='Y'">
        <!-- 自定义登录 -->
        <auth-login
          v-if="authMode=='auth'"
          :pwdOnOff="pwdOnOff"
          :captchaOnOff="captchaOnOff"
          :msgOnOff="msgOnOff"
        />
        <!-- 统一认证登录 -->
        <uaas-login v-if="authMode=='uaas'" :pwdOnOff="pwdOnOff" />
        <!-- ldap认证登录 -->
        <ldap-login
          v-if="authMode=='ldap'"
          :pwdOnOff="pwdOnOff"
          :captchaOnOff="captchaOnOff"
          :msgOnOff="msgOnOff"
        />
      </div>

    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>版权所有© 2018-2022 中国邮政储蓄银行</span>
    </div>
  </div>
</template>

<script>
import titleLogo from "@/assets/logo/logo3.png";
import slogon from "@/assets/images/title-text7.png";
import { loginType } from "@/api/login";
import UaasLogin from "./component/uaasLogin.vue";
import LdapLogin from "./component/ldapLogin.vue";
import AuthLogin from "./component/authLogin.vue";
import {getDictEntry, queryJoinSysList} from "../../api/tyjr";

export default {
  name: "Login",
  components: { UaasLogin, LdapLogin, AuthLogin },
  data() {
    return {
      titleLogo: titleLogo,
      slogon: slogon,
      authMode: "", //登录方式
      // 密码显示开关
      pwdOnOff: true,
      // 图片验证码开关
      captchaOnOff: false,
      //短信验证码开关
      msgOnOff: false,
      //邮连登录标识
      uniloginFlag: 'N'
    };
  },
  created() {
    this.getloginType(); //获取登录方式
    this.judgeUnilogin(); //判断是否只能邮连登录
  },
  methods: {
    tokenLogin() { //该方法不会调用，已在permission.js做了判断，若请求地址带userToken,则还没进入登录页，就已经被permission.js拦截转发到邮连登录的接口里
      let isUserToken = window.location.search.includes("userToken");
      if (isUserToken) {
        const userToken = this.parseQuery("userToken");
        this.$store
          .dispatch("UaasTokenLogin", { userToken })
          .then(() => {
            this.$router.push({ path: "/" });
          })
          .catch(() => {
            this.getloginType();
          });
      } else {
        this.getloginType();
      }
    },
    getloginType() {
      loginType()
        .then(res => {
          this.authMode = res.data.authMode;
          this.pwdOnOff = res.data.authCode.includes("00");
          this.captchaOnOff = res.data.authCode.includes("01");
          this.msgOnOff = res.data.authCode.includes("02");
        })
        .catch(() => {
          this.authMode = "auth";
        });
    },
    // 获取url参数
    parseQuery(key) {
      let result = {};
      let str = window.location.search.split("?")[1];
      let temp = str.split("&");
      temp.forEach(e => {
        let temp2 = e.split("=");
        result[temp2[0]] = temp2[1];
      });
      return result[key];
    },
    //判断是否只能邮连
    judgeUnilogin(){
      let dictTypeId = 'tyjr_unified_config';
      let dictId = 'unilogin_flag';
      getDictEntry(dictTypeId,dictId).then(res => {
        this.uniloginFlag = res.data.dictName;
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  position: fixed;
  height: 100%;
  width: 100%;
  background-image: url("../../assets/images/bg-new1.png");
  background-size: cover;

  .title-logo {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 300px;
  }

  .slogon {
    position: absolute;
    right: 0;
    top: 0;
    width: 80%;
    opacity: 0.8;
  }

  .login-form {
    position: absolute;
    width: 28%;
    z-index: 1;
    top: 40%;
    right: 8%;
    background: url("../../assets/images/bg-large.png") no-repeat 0 0;
    background-size: 100% 100%;
    padding: 25px;
    padding-bottom: 5px;

    .el-input {
      height: 38px;

      input {
        height: 38px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }

    .title {
      margin: 0px auto 30px auto;
      text-align: center;
      color: #fff;
      opacity: 0.8;
    }
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  width: 100%;
  height: 38px;
}
</style>
