{"name": "yoaf-web", "version": "1.1.2", "description": "新权限管理系统", "author": "front-end", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.19.1", "echarts": "4.9.0", "element-ui": "^2.15.6", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sm-crypto": "^0.3.12", "sortablejs": "1.10.2", "vue": "^2.7.14", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "yo-ui": "^2.2.11", "yo-utils": "^1.0.0"}, "devDependencies": {"@babel/preset-env": "^7.28.0", "babel-eslint": "10.1.0", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "mockjs": "^1.0.1-beta3", "runjs": "4.4.2", "sass": "1.32.13", "vite": "^4.5.0", "vite-plugin-vue2": "^2.0.3", "vue-template-compiler": "^2.7.16"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}