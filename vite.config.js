import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'path'
import { createVuePlugin } from 'vite-plugin-vue2'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isDev = command === 'serve'

  return {
    plugins: [
      createVuePlugin()
    ],
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    
    define: {
      'process.env': env
    },
    
    base: mode === 'production' ? '/uepsxmzhbgweb/' : '/uepsxmzhbgweb/',
    
    build: {
      outDir: 'dist',
      assetsDir: 'static',
      sourcemap: false,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            'chunk-libs': ['vue', 'vue-router', 'vuex', 'axios'],
            'chunk-elementUI': ['element-ui'],
            'chunk-commons': ['@riophae/vue-treeselect', 'echarts', 'quill']
          }
        }
      },
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production',
          pure_funcs: mode === 'production' ? ['console.log'] : []
        }
      }
    },
    
    server: {
      host: '0.0.0.0',
      port: 80,
      open: true,
      cors: true,
      proxy: {
        [env.VUE_APP_BASE_API + '/api/admin/xzp/']: {
          target: 'http://127.0.0.1:9091/',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VUE_APP_BASE_API}/api/admin/xzp/`), '/api/admin/xzp/')
        },
        [env.VUE_APP_BASE_API]: {
          target: 'http://127.0.0.1:9086/',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VUE_APP_BASE_API}`), '')
        }
      }
    },
    
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: ``
        }
      }
    },
    
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vuex',
        'axios',
        'element-ui',
        'echarts',
        'quill',
        'js-cookie',
        'nprogress',
        'clipboard',
        'screenfull',
        'sortablejs',
        'file-saver'
      ]
    }
  }
})
